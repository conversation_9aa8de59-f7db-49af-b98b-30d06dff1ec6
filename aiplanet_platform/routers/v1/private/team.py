"""
Router for team endpoints
"""

from typing import Dict, Optional
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    status,
    Request,
)
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.team import (
    TeamCreate,
    TeamList,
    TeamResponse,
    TeamUpdate,
    TeamTest,
)
from aiplanet_platform.services.team_service import TeamService
from aiplanet_platform.utils.update_resource import update_component_fields
from aiplanet_platform.services.team_validations import TeamValidationService

router = APIRouter(
    prefix="/teams",
    tags=["teams"],
)


@router.get("/", response_model=TeamList)
async def get_teams(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[list[str]] = Query([], description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db),
):
    """
    Get a list of teams with filtering, sorting, and pagination.
    """
    # Add organization id
    current_user = request.state.user

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if current_user.organization_id:
        filters["organization_id"] = current_user.organization_id

    service = TeamService(db)
    teams = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    # Get proper total count using the count method (for pagination)
    total_count = service.count_resources(filters)

    # Calculate total workflows for the organization (all teams, including inactive)
    # This should NOT include search/filter constraints - only organization filter
    org_filters = (
        {"organization_id": current_user.organization_id}
        if current_user.organization_id
        else {}
    )
    total_workflow = service.count_resources(org_filters)

    # Calculate active workflows for the organization (teams that are not deleted and are active)
    # This should also NOT include search/filter constraints - only organization filter
    active_filters = org_filters.copy()
    active_filters["is_active"] = True
    active_workflow = service.count_resources(active_filters)

    return {
        "items": teams,
        "total": total_count,
        "active_workflow": active_workflow,
        "total_workflow": total_workflow,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=TeamResponse)
async def get_team_by_id(
    resource_id: UUID = Path(..., description="ID of the team to get"),
    db: Session = Depends(get_db),
):
    """
    Get a specific team by ID.
    """
    service = TeamService(db)
    team = service.fetch_resource_by_id(resource_id)
    if not team:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return team


@router.post("/", response_model=TeamResponse, status_code=status.HTTP_201_CREATED)
async def create_team(
    request: Request,
    team_data: TeamCreate,
    db: Session = Depends(get_db),
):
    """
    Create a new team.
    """
    # Add organization id
    team_data.organization_id = request.state.user.organization_id

    # Validate ChatInput count
    validation_service = TeamValidationService(db)
    validation_service.validate_chat_input_count(team_data.team_input_ids)

    # Initialize service
    service = TeamService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": team_data.component.model_dump_json(),
            "organization_id": team_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(
        team_data.model_dump(
            exclude={
                "team_agent_ids": True,
                "team_input_ids": True,
                "team_output_ids": True,
                "team_termination_condition_ids": True,
            }
        ),
        team_data.team_agent_ids,
        team_data.team_input_ids,
        team_data.team_output_ids,
        team_data.team_termination_condition_ids,
    )


@router.put("/{resource_id}", response_model=TeamResponse)
async def update_team(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the team to update"),
    team_data: TeamUpdate = None,
    db: Session = Depends(get_db),
):
    """
    Update a team.
    """
    service = TeamService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = team_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    # Validate ChatInput count
    validation_service = TeamValidationService(db)
    validation_service.validate_chat_input_count(team_data.team_input_ids)

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    team_data.component = updated_data
    team_data.organization_id = request.state.user.organization_id

    team = service.update_resource_by_id(
        resource_id,
        team_data.model_dump(
            exclude={
                "team_agent_ids": True,
                "team_input_ids": True,
                "team_output_ids": True,
                "team_termination_condition_ids": True,
            }
        ),
        team_data.team_agent_ids,
        team_data.team_input_ids,
        team_data.team_output_ids,
        team_data.team_termination_condition_ids,
    )
    return team


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_team(
    resource_id: UUID = Path(..., description="ID of the team to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    db: Session = Depends(get_db),
):
    """
    Delete a team. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    service = TeamService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=TeamResponse)
async def restore_team(
    resource_id: UUID = Path(..., description="ID of the team to restore", gt=0),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted team.
    """
    service = TeamService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource


@router.post("/{resource_id}/deploy")
async def deploy_team(
    resource_id: UUID = Path(..., description="ID of the team to deploy"),
    db: Session = Depends(get_db),
):
    """
    Deploy team.
    """
    service = TeamService(db)
    return service.deploy_resource_by_id(resource_id)


@router.post("/{resource_id}/stop-deployment")
async def undeploy_team(
    resource_id: UUID = Path(..., description="ID of the team to undeploy"),
    db: Session = Depends(get_db),
):
    """
    Undeploy team.
    """
    service = TeamService(db)
    return service.undeploy_resource_by_id(resource_id)


@router.post("/{resource_id}/test")
async def test_team(
    resource_id: UUID = Path(..., description="ID of the team to test"),
    test_data: TeamTest = None,
    db: Session = Depends(get_db),
):
    """
    Test team.
    """
    service = TeamService(db)
    return await service.test_resource_by_id(resource_id, test_data.model_dump())
