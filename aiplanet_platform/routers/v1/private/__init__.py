"""API package"""
from fastapi import APIRouter, Depends

from aiplanet_platform.core.enhanced_security import get_current_user_or_api_key
from aiplanet_platform.routers.v1.private.agent import router as agent_router
from aiplanet_platform.routers.v1.private.builder import router as builder_router
from aiplanet_platform.routers.v1.private.input import router as input_router
from aiplanet_platform.routers.v1.private.model import router as model_router
from aiplanet_platform.routers.v1.private.organization import (
    router as organization_router,
)
from aiplanet_platform.routers.v1.private.output import router as output_router
from aiplanet_platform.routers.v1.private.team import router as team_router
from aiplanet_platform.routers.v1.private.termination_condition import (
    router as termination_condition_router,
)
from aiplanet_platform.routers.v1.private.tool import router as tool_router
from aiplanet_platform.routers.v1.private.user import router as user_router
from aiplanet_platform.routers.v1.private.session import router as session_router
from aiplanet_platform.routers.v1.private.validation import router as validation_router
from aiplanet_platform.routers.v1.private.api_key import router as api_key_router
from aiplanet_platform.routers.v1.private.user_management import (
    router as user_management_router,
)

router = APIRouter(
    prefix="/private",
    tags=["private"],
    dependencies=[
        Depends(get_current_user_or_api_key)
    ],  # ✅ Enforces auth on all routes under this router
)

# Include routers
router.include_router(user_management_router)
router.include_router(user_router)
router.include_router(organization_router)
router.include_router(builder_router)
router.include_router(model_router)
router.include_router(tool_router)
router.include_router(termination_condition_router)
router.include_router(output_router)
router.include_router(input_router)
router.include_router(agent_router)
router.include_router(team_router)
router.include_router(session_router)
router.include_router(validation_router)
router.include_router(api_key_router)
