"""
Router for model endpoints
"""
from typing import Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status, Request
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.schemas.model import (
    ModelCreate,
    ModelList,
    ModelResponse,
    ModelUpdate,
)
from aiplanet_platform.services.model_service import ModelService
from aiplanet_platform.utils.update_resource import update_component_fields

router = APIRouter(
    prefix="/models",
    tags=["models"],
)


@router.get("/", response_model=ModelList)
async def get_models(
    request: Request,
    skip: int = Query(0, description="Number of records to skip", ge=0),
    limit: int = Query(100, description="Maximum number of records to return", le=1000),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", description="Sort order (asc or desc)"),
    name: Optional[str] = Query(None, description="Filter by name"),
    name_like: Optional[str] = Query(
        None, description="Filter by name (partial match)"
    ),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db),
):
    """
    Get a list of models with filtering, sorting, and pagination.
    """
    user = request.state.user

    # Build filters dictionary from query parameters
    filters = {}
    if name:
        filters["name"] = name
    if name_like:
        filters["name_like"] = name_like
    if is_active is not None:
        filters["is_active"] = is_active
    if user.organization_id:
        filters["organization_id"] = user.organization_id

    service = ModelService(db)
    models = service.fetch_resource_by_filters(
        filters, skip=skip, limit=limit, sort_by=sort_by, sort_order=sort_order
    )

    total_count = len(
        models
    )  # This should be replaced with a proper count query in a real app

    return {
        "items": models,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1,
        "size": limit,
    }


@router.get("/{resource_id}", response_model=ModelResponse)
async def get_model_by_id(
    resource_id: UUID = Path(..., description="ID of the model to get"),
    db: Session = Depends(get_db),
):
    """
    Get a specific model by ID.
    """
    service = ModelService(db)
    model = service.fetch_resource_by_id(resource_id)
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )
    return model


@router.post("/", response_model=ModelResponse, status_code=status.HTTP_201_CREATED)
async def create_model(
    request: Request,
    model_data: ModelCreate,
    db: Session = Depends(get_db),
):
    """
    Create a new model.
    """
    # Add organization id
    model_data.organization_id = request.state.user.organization_id

    # Instantiate service
    service = ModelService(db)

    # Check if a resource with the same name already exists
    existing = service.fetch_resource_by_filters(
        {
            "component": model_data.component.model_dump_json(),
            "organization_id": model_data.organization_id,
        }
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Resource with this name already exists",
        )

    return service.create_resource(model_data.model_dump())


@router.put("/{resource_id}", response_model=ModelResponse)
async def update_model(
    request: Request,
    resource_id: UUID = Path(..., description="ID of the model to update"),
    model_data: ModelUpdate = None,
    db: Session = Depends(get_db),
):
    """
    Update a model.
    """
    # Add organization id
    model_data.organization_id = request.state.user.organization_id

    # Instantiate service
    service = ModelService(db)

    # Verify resource exists
    existing = service.fetch_resource_by_id(resource_id)
    if not existing:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    # Exclude None values to implement partial updates
    data = model_data.component.model_dump(exclude_unset=True)
    if not data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="No data provided"
        )

    existing_data = existing.component

    updated_data = update_component_fields(existing_data, data)
    model_data.component = updated_data
    model_data.organization_id = request.state.user.organization_id

    model = service.update_resource_by_id(resource_id, model_data.model_dump())
    return model


@router.delete("/{resource_id}", response_model=Dict[str, bool])
async def delete_model(
    resource_id: UUID = Path(..., description="ID of the model to delete"),
    permanent: bool = Query(False, description="Permanently delete the resource"),
    db: Session = Depends(get_db),
):
    """
    Delete a model. By default, this is a soft delete.
    Set permanent=true to permanently delete the resource.
    """
    service = ModelService(db)

    if permanent:
        success = service.hard_delete_resource_by_id(resource_id)
    else:
        success = service.remove_resource_by_id(resource_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    return {"success": True}


@router.post("/{resource_id}/restore", response_model=ModelResponse)
async def restore_model(
    resource_id: UUID = Path(..., description="ID of the model to restore", gt=0),
    db: Session = Depends(get_db),
):
    """
    Restore a soft-deleted model.
    """
    service = ModelService(db)

    # Try to fetch the resource including deleted ones
    query = db.query(service.model).filter(service.model.id == resource_id)
    resource = query.first()

    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Resource not found"
        )

    if not resource.is_deleted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Resource is not deleted"
        )

    # Restore the resource
    resource.is_deleted = False
    db.commit()
    db.refresh(resource)

    return resource
