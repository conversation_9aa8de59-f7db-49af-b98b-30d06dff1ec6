"""
Router for settings endpoints
"""
import math
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session

from aiplanet_platform.core.database import get_db
from aiplanet_platform.core.security import get_current_user
from aiplanet_platform.models.user import User
from aiplanet_platform.services.settings_service import SettingsService
from aiplanet_platform.schemas.settings import (
    SettingsCreate,
    SettingsUpdate,
    SettingsResponse,
    SettingsList,
)

router = APIRouter(
    prefix="/settings",
    tags=["settings"],
)


@router.get("/", response_model=SettingsList)
async def get_settings_list(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get a paginated list of settings for the current user.

    You can filter, sort, and paginate the results.
    """
    service = SettingsService(db)

    # Filter by current user
    filters = {"user_id": current_user.id}

    # Get total count for pagination
    total_items = service.fetch_resource_by_filters(filters)
    total = len(total_items)

    # Get paginated results
    settings = service.fetch_resource_by_filters(
        filters=filters,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )

    # Calculate pagination info
    pages = math.ceil(total / limit) if limit > 0 else 1
    page = (skip // limit) + 1 if limit > 0 else 1

    return SettingsList(
        items=settings,
        total=total,
        page=page,
        size=len(settings),
        pages=pages,
    )


@router.get("/{settings_id}", response_model=SettingsResponse)
async def get_settings(
    settings_id: UUID = Path(..., description="ID of the settings to get"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get details of specific settings by ID.

    Only returns settings that belong to the current user.
    """
    service = SettingsService(db)
    settings = service.fetch_resource_by_filters(
        {
            "id": settings_id,
            "user_id": current_user.id,
        }
    )

    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settings not found"
        )

    return settings[0]


@router.post("/", response_model=SettingsResponse, status_code=status.HTTP_201_CREATED)
async def create_settings(
    settings_data: SettingsCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Create new settings for the current user.

    Each user can have multiple settings configurations.
    """
    service = SettingsService(db)

    # Set user_id to current user
    create_data = settings_data.model_dump()
    create_data["user_id"] = current_user.id

    return service.create_resource(create_data)


@router.put("/{settings_id}", response_model=SettingsResponse)
async def update_settings(
    update_data: SettingsUpdate,
    settings_id: UUID = Path(..., description="ID of the settings to update"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update settings by ID.

    Only allows updating settings that belong to the current user.
    You can partially update the configuration.
    """
    service = SettingsService(db)

    # Verify settings exists and belongs to current user
    existing_settings = service.fetch_resource_by_filters(
        {
            "id": settings_id,
            "user_id": current_user.id,
        }
    )

    if not existing_settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settings not found"
        )

    # Convert Pydantic model to dict, excluding unset fields
    update_dict = update_data.model_dump(exclude_unset=True)

    updated_settings = service.update_resource_by_id(settings_id, update_dict)

    if not updated_settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settings not found"
        )

    return updated_settings


@router.delete("/{settings_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_settings(
    settings_id: UUID = Path(..., description="ID of the settings to delete"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Soft delete settings by ID.

    Only allows deleting settings that belong to the current user.
    The settings will be marked as deleted but not permanently removed.
    """
    service = SettingsService(db)

    # Verify settings exists and belongs to current user
    existing_settings = service.fetch_resource_by_filters(
        {
            "id": settings_id,
            "user_id": current_user.id,
        }
    )

    if not existing_settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settings not found"
        )

    success = service.remove_resource_by_id(settings_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Settings not found"
        )


@router.get("/user/current", response_model=SettingsResponse)
async def get_current_user_settings(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get the most recent settings for the current user.

    Returns the latest settings configuration for the authenticated user.
    If no settings exist, returns a 404 error.
    """
    service = SettingsService(db)

    # Get user's settings, sorted by creation date (most recent first)
    settings = service.fetch_resource_by_filters(
        filters={"user_id": current_user.id},
        skip=0,
        limit=1,
        sort_by="created_at",
        sort_order="desc",
    )

    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No settings found for current user"
        )

    return settings[0]