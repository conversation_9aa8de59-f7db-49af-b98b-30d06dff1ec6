"""
Router for oauth endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from authlib.integrations.base_client import OAuthError

from aiplanet_platform.core.database import get_db
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.constants.user import UserStatus
from aiplanet_platform.services.organization_service import OrganizationService
from aiplanet_platform.services.oauth_service import OAuthService
from aiplanet_platform.constants.organization import OrganizationStatus
from aiplanet_platform.schemas.user import TokenResponse
from aiplanet_platform.core.security import create_access_token, create_refresh_token


router = APIRouter(
    prefix="/oauths",
    tags=["oauths"],
)


@router.get("/{provider}/login")
async def oauth_login(
    provider: str,
    db: Session = Depends(get_db),
):
    """
    Initiate OAuth login for the specified provider.

    Args:
        provider: OAuth provider ('google' or 'github')

    Returns:
        Authorization URL and state parameter
    """

    oauth_service = OAuthService(db)
    if provider not in ["google", "github"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported OAuth provider. Use 'google' or 'github'.",
        )

    state = oauth_service.generate_state()
    authorization_url = oauth_service.get_authorization_url(provider, state)

    return {"authorization_url": authorization_url, "state": state}


@router.get("/{provider}/callback")
async def oauth_callback(
    provider: str,
    code: str = Query(..., description="Authorization code from OAuth provider"),
    state: str = Query(..., description="State parameter for security"),
    db: Session = Depends(get_db),
):
    """
    Handle OAuth callback and create/login user.

    Args:
        provider: OAuth provider ('google' or 'github')
        code: Authorization code from provider
        state: State parameter for security validation
        db: Database session

    Returns:
        JWT tokens for authenticated user
    """
    oauth_service = OAuthService(db)
    if provider not in ["google", "github"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Unsupported OAuth provider"
        )

    try:
        # Exchange code for access token
        token_data = await oauth_service.exchange_code_for_token(provider, code)
        access_token = token_data.get("access_token")

        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to obtain access token",
            )

        # Get user info from provider
        user_info = await oauth_service.get_user_info(provider, access_token)

        # Extract user data
        email = user_info.get("email")
        name = user_info.get("name") or user_info.get("login")  # GitHub uses 'login'

        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email not provided by OAuth provider",
            )

        # Find or create user
        user_service = UserService(db)
        existing_user = user_service.fetch_resource_by_filters({"email": email})

        if existing_user:
            # User exists, log them in
            user = existing_user[0]
        else:
            # Create new user
            org_service = OrganizationService(db)

            # Check if organization with this email exists
            existing_org = org_service.fetch_resource_by_filters({"email": email})
            if existing_org:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Organization with this email already exists",
                )

            # Create organization
            organization = org_service.create_resource(
                {
                    "email": email,
                    "name": name or email.split("@")[0],
                    "status": OrganizationStatus.ACTIVE,
                }
            )

            # Create user
            user = user_service.create_resource(
                {
                    "email": email,
                    "name": name or email.split("@")[0],
                    "password": "",  # OAuth users don't have passwords
                    "organization_id": organization.id,
                    "oauth_provider": provider,
                    "oauth_id": str(user_info.get("id", "")),
                    "status": UserStatus.ACTIVE,
                }
            )

        # Create JWT tokens
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        refresh_token = create_refresh_token(
            data={"sub": str(user.id), "email": user.email}
        )

        return TokenResponse(
            access_token=access_token, refresh_token=refresh_token, token_type="bearer"
        )

    except OAuthError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth authentication failed: {str(e)}",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication failed: {str(e)}",
        )


@router.get("/providers")
async def get_oauth_providers():
    """
    Get list of available OAuth providers.

    Returns:
        List of available OAuth providers and their configuration
    """
    return {
        "providers": [
            {
                "name": "google",
                "display_name": "Google",
                "login_endpoint": "/api/v1/public/oauth/google/login",
            },
            {
                "name": "github",
                "display_name": "GitHub",
                "login_endpoint": "/api/v1/public/oauth/github/login",
            },
        ]
    }
