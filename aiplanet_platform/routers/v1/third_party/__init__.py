# aiplanet_platform/routers/v1/third_party/__init__.py
"""
Third-party API router.
This module provides API endpoints for external applications using API keys.
"""
from fastapi import APIRouter

from aiplanet_platform.routers.v1.third_party.health import router as health_router
from aiplanet_platform.routers.v1.third_party.organization import (
    router as organization_router,
)
from aiplanet_platform.routers.v1.third_party.user import router as user_router

router = APIRouter(
    prefix="/third-party",
    tags=["third-party apis"],
)

# Include all third-party routers
router.include_router(health_router)
router.include_router(user_router)
router.include_router(organization_router)

# You can add more routers here as you expand the third-party API:
# from aiplanet_platform.routers.v1.third_party.agent import router as agent_router
# from aiplanet_platform.routers.v1.third_party.tool import router as tool_router
# from aiplanet_platform.routers.v1.third_party.session import router as session_router
# router.include_router(agent_router)
# router.include_router(tool_router)
# router.include_router(session_router)
