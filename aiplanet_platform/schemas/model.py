"""
Pydantic schemas for model
"""
from datetime import datetime
from typing import List, Optional, Union
from uuid import UUID

from autogen_core import ComponentModel
from pydantic import BaseModel, Field


class ModelBase(BaseModel):
    """Base schema for model"""

    component: Union[ComponentModel, dict] = Field(
        ..., description="Component of the model"
    )
    organization_id: Optional[UUID] = Field(None, description="Organization ID")
    # Add your custom fields here


class ModelCreate(ModelBase):
    """Schema for creating a model"""

    # Add creation-specific fields here
    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                    "component_type": "model",
                    "version": 1,
                    "component_version": 1,
                    "description": "Chat completion client for Azure OpenAI hosted models.",
                    "label": "AzureOpenAIChatCompletionClient",
                    "config": {
                        "model": "gpt-4o-mini",
                        "api_key": "**********",
                        "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                        "azure_deployment": "{your-azure-deployment}",
                        "api_version": "2024-06-01",
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                # Add example values for other fields
            }
        }


class ModelUpdate(ModelBase):
    """Schema for updating a model"""

    class Config:
        json_schema_extra = {
            "example": {
                "component": {
                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                    "component_type": "model",
                    "version": 1,
                    "component_version": 1,
                    "description": "UpdateChat completion client for Azure OpenAI hosted models.",
                    "label": "AzureOpenAIChatCompletionClient",
                    "config": {
                        "model": "gpt-4o-mini",
                        "api_key": "**********",
                        "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                        "azure_deployment": "{your-azure-deployment}",
                        "api_version": "2024-06-01",
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                # Add example values for other fields
            }
        }


class ModelResponse(ModelBase):
    """Schema for model response"""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    is_deleted: bool = Field(False, description="Whether the resource is deleted")
    # Include additional fields from your model here

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "component": {
                    "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                    "component_type": "model",
                    "version": 1,
                    "component_version": 1,
                    "description": "UpdateChat completion client for Azure OpenAI hosted models.",
                    "label": "AzureOpenAIChatCompletionClient",
                    "config": {
                        "model": "gpt-4o-mini",
                        "api_key": "**********",
                        "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                        "azure_deployment": "{your-azure-deployment}",
                        "api_version": "2024-06-01",
                    },
                },
                "organization_id": "123e4567-e89b-12d3-a456-************",
                "created_at": "2023-01-01T00:00:00Z",
                "updated_at": "2023-01-02T00:00:00Z",
                "is_deleted": False,
            }
        }


class ModelList(BaseModel):
    """Schema for paginated list of models"""

    items: List[ModelResponse] = Field(..., description="List of models")
    total: int = Field(..., description="Total number of models")
    page: int = Field(1, description="Current page number")
    pages: int = Field(1, description="Total number of pages")
    size: int = Field(100, description="Number of items per page")

    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "UpdateChat completion client for Azure OpenAI hosted models.",
                            "label": "AzureOpenAIChatCompletionClient",
                            "config": {
                                "model": "gpt-4o-mini",
                                "api_key": "**********",
                                "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                "azure_deployment": "{your-azure-deployment}",
                                "api_version": "2024-06-01",
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                    {
                        "id": "123e4567-e89b-12d3-a456-************",
                        "component": {
                            "provider": "autogen_ext.models.openai.AzureOpenAIChatCompletionClient",
                            "component_type": "model",
                            "version": 1,
                            "component_version": 1,
                            "description": "UpdateChat completion client for Azure OpenAI hosted models.",
                            "label": "AzureOpenAIChatCompletionClient",
                            "config": {
                                "model": "gpt-4o-mini",
                                "api_key": "**********",
                                "azure_endpoint": "https://{your-custom-endpoint}.openai.azure.com/",
                                "azure_deployment": "{your-azure-deployment}",
                                "api_version": "2024-06-01",
                            },
                        },
                        "organization_id": "123e4567-e89b-12d3-a456-************",
                        "created_at": "2023-01-01T00:00:00Z",
                        "updated_at": "2023-01-02T00:00:00Z",
                        "is_deleted": False,
                    },
                ],
                "total": 2,
                "page": 1,
                "pages": 1,
                "size": 100,
            }
        }
