"""
Pydantic schemas for settings
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from aiplanet_platform.constants.team_manager import SettingsConfig


class SettingsBase(BaseModel):
    """Base schema for settings"""
    
    config: Optional[Dict[str, Any]] = Field(
        None, description="Settings configuration as JSON"
    )


class SettingsCreate(SettingsBase):
    """Schema for creating settings"""
    
    user_id: Optional[UUID] = Field(
        None, description="User ID (will be set from current user if not provided)"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "config": {
                    "environment": [
                        {
                            "name": "API_KEY",
                            "value": "your-api-key",
                            "type": "secret",
                            "description": "API key for external service",
                            "required": True
                        }
                    ],
                    "default_model_client": {
                        "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient",
                        "component_type": "model",
                        "version": 1,
                        "component_version": 1,
                        "config": {
                            "model": "gpt-4o-mini",
                            "api_key": "your-api-key"
                        }
                    },
                    "ui": {
                        "show_llm_call_events": False,
                        "expanded_messages_by_default": True,
                        "show_agent_flow_by_default": True
                    }
                }
            }
        }


class SettingsUpdate(SettingsBase):
    """Schema for updating settings"""
    
    class Config:
        json_schema_extra = {
            "example": {
                "config": {
                    "ui": {
                        "show_llm_call_events": True,
                        "expanded_messages_by_default": False,
                        "show_agent_flow_by_default": True
                    }
                }
            }
        }


class SettingsResponse(BaseModel):
    """Schema for settings responses"""
    
    id: UUID
    user_id: UUID
    config: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class SettingsList(BaseModel):
    """Schema for paginated settings list"""
    
    items: List[SettingsResponse]
    total: int
    page: int
    size: int
    pages: int
    
    class Config:
        from_attributes = True
