"""
Constants related to api key
"""
from datetime import datetime, timedelta


API_PERMISSIONS = [
    {
        "scope": "*",
        "description": "All Permissions",
        "category": "Admin",
    },
    {
        "scope": "read:users",
        "description": "Read user information",
        "category": "Users",
    },
    {
        "scope": "write:users",
        "description": "Create and update users",
        "category": "Users",
    },
    {
        "scope": "read:organizations",
        "description": "Read organization information",
        "category": "Organizations",
    },
    {
        "scope": "write:organizations",
        "description": "Update organization information",
        "category": "Organizations",
    },
    {
        "scope": "read:agents",
        "description": "Read AI agents and configurations",
        "category": "AI Agents",
    },
    {
        "scope": "write:agents",
        "description": "Create and update AI agents",
        "category": "AI Agents",
    },
    {
        "scope": "read:sessions",
        "description": "Read session information",
        "category": "Sessions",
    },
    {
        "scope": "write:sessions",
        "description": "Create and manage sessions",
        "category": "Sessions",
    },
    {
        "scope": "read:models",
        "description": "Read model configurations",
        "category": "Models",
    },
    {
        "scope": "write:models",
        "description": "Create and update model configurations",
        "category": "Models",
    },
    {
        "scope": "read:tools",
        "description": "Read tool configurations",
        "category": "Tools",
    },
    {
        "scope": "write:tools",
        "description": "Create and update tool configurations",
        "category": "Tools",
    },
    {
        "scope": "read:termination_conditions",
        "description": "Read termination condition configurations",
        "category": "Tools",
    },
    {
        "scope": "write:termination_conditions",
        "description": "Create and update termination condition configurations",
        "category": "Tools",
    },
    {
        "scope": "read:teams",
        "description": "Read team configurations",
        "category": "Teams",
    },
    {
        "scope": "write:teams",
        "description": "Create and update team configurations",
        "category": "Teams",
    },
    {
        "scope": "write:roles",
        "description": "Create and update Roles",
        "category": "Models",
    },
    {
        "scope": "read:chats",
        "description": "Read chat configurations",
        "category": "Chats",
    },
    {
        "scope": "write:chats",
        "description": "Create and update chat configurations",
        "category": "Chats",
    },
]

API_KEY_RATE_LIMITS = {
    "rate_limit_per_minute": 100,
    "rate_limit_per_hour": 1000,
    "rate_limit_per_day": 10000,
    "expires_at": datetime.now() + timedelta(days=365),
}
