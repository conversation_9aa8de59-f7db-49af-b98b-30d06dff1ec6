import logging
import yfinance as yf
import yt_dlp
import wikipedia
from googlesearch import search
from youtube_search import YoutubeSearch
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor
from autogen_ext.tools.code_execution import PythonCode<PERSON>xecutionTool
from autogen_core.tools import FunctionTool

logger = logging.getLogger(__name__)
# Generate the config using the same logic as in BuilderService
python_code_execution_tool = PythonCodeExecutionTool(
    LocalCommandLineCodeExecutor(work_dir="coding")
)


tool_component = python_code_execution_tool.dump_component()
tool_component_dict = (
    tool_component.model_dump()
    if hasattr(tool_component, "model_dump")
    else tool_component
)
# Set a custom label for Python code execution tool
tool_component_dict["label"] = "PythonCodeExecutionTool"


# Define the Google search function
async def search_web(query: str, num_results: int = 2, language: str = "en") -> str:
    logger.debug(
        f"[DEBUG] search_web called with query='{query}', num_results={num_results}, language='{language}'"
    )
    try:
        # Perform the search
        results = list(
            search(
                query,
                num_results=num_results + 2,
                lang=language,
                proxy=None,
                advanced=True,
            )
        )
        logger.debug(f"[DEBUG] Raw search results: {results}")

        # Process results
        formatted_results = ""
        for result in results:
            title = getattr(result, "title", "")
            url = getattr(result, "url", "")
            description = getattr(result, "description", "")

            # Filter out invalid results
            if title and url and url != "/" and description and description != "/":
                formatted_results += f"Title: {title or 'No title'}\n"
                formatted_results += f"URL: {url or ''}\n"
                formatted_results += (
                    f"Description: {description or 'No description'}\n\n"
                )

                # Break once we have enough valid results
                if formatted_results.count("Title:") >= num_results:
                    break

        # Return empty result if nothing found
        if not formatted_results:
            logger.debug("[DEBUG] No valid formatted results found.")
            formatted_results = "No results found."
        else:
            logger.debug(f"[DEBUG] Formatted results: {formatted_results}")

        return formatted_results
    except Exception as e:
        logger.error(f"[ERROR] Exception in search_web: {e}")
        return f"Search error: {str(e)}"


google_search_tool = FunctionTool(
    search_web, name="web_search", description="Search the web for current information"
)
logger.debug("[DEBUG] google_search_tool created.")
# Convert to dictionary for storage
google_tool_component = google_search_tool.dump_component()
google_tool_component_dict = (
    google_tool_component.model_dump()
    if hasattr(google_tool_component, "model_dump")
    else google_tool_component
)
# Set a custom label for Google search tool
google_tool_component_dict["label"] = "WebSearchTool"


# Add Yahoo Finance tool function
async def yahoo_finance_tool(
    symbol: str, info_type: str = "summary", period: str = "1d"
) -> str:
    """Yahoo Finance tool for fetching stock market data.

    Args:
        symbol: Stock symbol to look up (e.g., 'AAPL' for Apple)
        info_type: Type of information to retrieve: 'summary', 'price', 'history', or 'info'
        period: Time period for historical data (e.g., '1d', '5d', '1mo', '1y')

    Returns:
        Formatted financial information
    """
    try:
        stock = yf.Ticker(symbol)

        if info_type == "summary":
            info = stock.info
            return (
                f"Company: {info.get('longName', 'N/A')}\n"
                f"Current Price: ${info.get('currentPrice', 'N/A')}\n"
                f"Market Cap: ${info.get('marketCap', 'N/A')}\n"
                f"52 Week High: ${info.get('fiftyTwoWeekHigh', 'N/A')}\n"
                f"52 Week Low: ${info.get('fiftyTwoWeekLow', 'N/A')}"
            )

        elif info_type == "price":
            return (
                f"Current price of {symbol}: ${stock.info.get('currentPrice', 'N/A')}"
            )

        elif info_type == "history":
            history = stock.history(period=period)
            if history.empty:
                return f"No historical data available for {symbol}"

            latest = history.iloc[-1]
            return (
                f"Historical data for {symbol} (last entry):\n"
                f"Date: {latest.name.date()}\n"
                f"Open: ${latest['Open']:.2f}\n"
                f"High: ${latest['High']:.2f}\n"
                f"Low: ${latest['Low']:.2f}\n"
                f"Close: ${latest['Close']:.2f}\n"
                f"Volume: {latest['Volume']}"
            )

        elif info_type == "info":
            info = stock.info
            return (
                f"Company Information for {symbol}:\n"
                f"Industry: {info.get('industry', 'N/A')}\n"
                f"Sector: {info.get('sector', 'N/A')}\n"
                f"Website: {info.get('website', 'N/A')}\n"
                f"Description: {info.get('longBusinessSummary', 'N/A')}"
            )

        else:
            return f"Invalid info_type: {info_type}. Supported types are: summary, price, history, info"

    except Exception as e:
        return f"Error fetching data for {symbol}: {str(e)}"


# Create the Yahoo Finance tool
logger.debug("[DEBUG] Creating yahoo_finance_tool...")
yahoo_finance_tool_obj = FunctionTool(
    yahoo_finance_tool,
    name="yahoo_finance",
    description="Get stock market data from Yahoo Finance",
)
logger.debug("[DEBUG] yahoo_finance_tool created.")
# Convert to dictionary for storage
yahoo_finance_component = yahoo_finance_tool_obj.dump_component()
yahoo_finance_component_dict = (
    yahoo_finance_component.model_dump()
    if hasattr(yahoo_finance_component, "model_dump")
    else yahoo_finance_component
)
# Set a custom label for Yahoo Finance tool
yahoo_finance_component_dict["label"] = "YahooFinanceTool"


# Add YouTube search tool function
async def youtube_search_tool(query: str, max_results: int = 5) -> str:
    """YouTube Search Tool

    Searches for videos on YouTube and extracts their titles and descriptions.

    Args:
        query: Keyword required to search the video content on YouTube
        max_results: Total results to be executed from the search. Defaults to 5

    Returns:
        Formatted string containing video titles and descriptions
    """

    ydl_opts = {
        "quiet": True,
        "skip_download": True,
        "force_generic_extractor": True,
        "format": "best",
    }

    results = YoutubeSearch(query, max_results=max_results)
    response = results.to_dict()
    context = ""

    for ids in response:
        url = "https://youtube.com/watch?v=" + ids["id"]
        context += f"Title: {ids['title']}\n"

        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info_dict = ydl.extract_info(url, download=False)
                description = info_dict.get("description", "No description available")
                context += f"Description: {description}\n\n"
        except Exception as e:
            context += f"Description: Error extracting description - {str(e)}\n\n"

    return context.strip()


# Create the YouTube search tool
logger.debug("[DEBUG] Creating youtube_search_tool...")
youtube_search_tool_obj = FunctionTool(
    youtube_search_tool,
    name="youtube_search",
    description="Search for videos on YouTube and extract their titles and descriptions",
)
logger.debug("[DEBUG] youtube_search_tool created.")
# Convert to dictionary for storage
youtube_search_component = youtube_search_tool_obj.dump_component()
youtube_search_component_dict = (
    youtube_search_component.model_dump()
    if hasattr(youtube_search_component, "model_dump")
    else youtube_search_component
)
# Set a custom label for YouTube search tool
youtube_search_component_dict["label"] = "YouTubeSearchTool"


# Add Wikipedia search tool function
async def wikipedia_search(query: str, max_results: int = 3) -> str:
    """Search Wikipedia for a query and return article information.

    Args:
        query: Query to search Wikipedia for
        max_results: Maximum number of sentences to return from the Wikipedia article

    Returns:
        Formatted string with Wikipedia article information
    """
    try:
        # Search Wikipedia
        search_results = wikipedia.search(query)

        if not search_results:
            return "No Wikipedia results found for your query."

        # Get the first (most relevant) page
        try:
            page = wikipedia.page(search_results[0])
            summary = wikipedia.summary(search_results[0], sentences=max_results)

            return f"Title: {page.title}\n" f"URL: {page.url}\n" f"Summary: {summary}"

        except wikipedia.DisambiguationError as e:
            # Handle disambiguation pages
            options = e.options[:5]  # Return first 5 options
            return (
                f"Your query '{query}' is ambiguous. Did you mean one of these?\n"
                f"{', '.join(options)}"
            )

    except Exception as e:
        return f"Error searching Wikipedia: {str(e)}"


# Create the Wikipedia search tool
logger.debug("[DEBUG] Creating wikipedia_search_tool...")
wikipedia_search_tool_obj = FunctionTool(
    wikipedia_search,
    name="wikipedia_search",
    description="Search Wikipedia for information on a given query",
)
logger.debug("[DEBUG] wikipedia_search_tool created.")
# Convert to dictionary for storage
wikipedia_search_component = wikipedia_search_tool_obj.dump_component()
wikipedia_search_component_dict = (
    wikipedia_search_component.model_dump()
    if hasattr(wikipedia_search_component, "model_dump")
    else wikipedia_search_component
)
# Set a custom label for Wikipedia search tool
wikipedia_search_component_dict["label"] = "WikipediaSearchTool"

# Add to DEFAULT_TOOLS list
DEFAULT_TOOLS = [
    {
        "component": tool_component_dict,
        "organization_id": None,  # This will be set in bulk_add_tools
    },
    {
        "component": google_tool_component_dict,
        "organization_id": None,  # This will be set in bulk_add_tools
    },
    {
        "component": yahoo_finance_component_dict,
        "organization_id": None,  # This will be set in bulk_add_tools
    },
    {
        "component": youtube_search_component_dict,
        "organization_id": None,  # This will be set in bulk_add_tools
    },
    {
        "component": wikipedia_search_component_dict,
        "organization_id": None,  # This will be set in bulk_add_tools
    },
]
