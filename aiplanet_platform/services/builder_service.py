"""
Service for builder operations
"""

from typing import Any, Dict, List

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import (
    ExternalTermination,
    FunctionCallTermination,
    HandoffTermination,
    MaxMessageTermination,
    SourceMatchTermination,
    StopMessageTermination,
    TextMentionTermination,
    TextMessageTermination,
    TimeoutTermination,
    TokenUsageTermination,
)
from autogen_agentchat.teams import (
    RoundRobinGroupChat,
    SelectorGroupChat,
    Swarm,
)
from autogen_core import ComponentModel
from autogen_core.tools import FunctionTool

# from autogen_core.models import ModelInfo
# from autogen_ext.agents.web_surfer import MultimodalWebSurfer
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor
from autogen_ext.models.anthropic import AnthropicChatCompletionClient
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.openai._openai_client import AzureOpenAIChatCompletionClient
from autogen_ext.tools.code_execution import Python<PERSON>ode<PERSON>xecutionTool
from fastapi import Depends
from sqlalchemy.orm import Session

from aiplanet_platform.constants.input import (
    FileInputConfig,
    ImageInputConfig,
    TextInputConfig,
    URLInputConfig,
    ChatInputConfig,
)
from aiplanet_platform.constants.output import (
    JSONOutputConfig,
    MarkdownOutputConfig,
    TextOutputConfig,
)
from aiplanet_platform.core.database import get_db


class BuilderService:
    """Service for builder operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    def get_model_configs(self) -> List[Dict[str, Any]]:
        """
        Get all model configs.
        """
        anthropic_provider = AnthropicChatCompletionClient(
            model="claude-3-7-sonnet-20250219"
        ).dump_component()

        openai_provider = OpenAIChatCompletionClient(
            model="gpt-4", api_key="{your-openai-key}", max_tokens=1000, temperature=0.7
        ).dump_component()

        azure_openai_provider = AzureOpenAIChatCompletionClient(
            azure_deployment="{your-azure-deployment}",
            model="gpt-4o-mini",
            api_version="2024-06-01",
            azure_endpoint="https://{your-custom-endpoint}.openai.azure.com/",
            api_key="test",
            max_tokens=1000,
            temperature=0.7,
        ).dump_component()

        return [anthropic_provider, openai_provider, azure_openai_provider]

    def get_termination_condition_configs(self) -> List[Dict[str, Any]]:
        """
        Get all termination conditions.
        """
        max_message_termination = MaxMessageTermination(max_messages=10)
        text_mention_termination = TextMentionTermination(text="TERMINATE")
        stop_message_termination = StopMessageTermination()
        token_usage_termination = TokenUsageTermination(
            max_total_token=100, max_prompt_token=100, max_completion_token=100
        )
        handoff_termination = HandoffTermination(target="assistant_agent")
        timeout_termination = TimeoutTermination(timeout_seconds=1000)
        external_termination = ExternalTermination()
        source_match_termination = SourceMatchTermination(sources=["assistant_agent"])
        text_message_termination = TextMessageTermination(source="assistant_agent")
        function_call_termination = FunctionCallTermination(function_name="TERMINATE")

        calc_or_term = max_message_termination | text_mention_termination
        return [
            max_message_termination.dump_component(),
            text_mention_termination.dump_component(),
            calc_or_term.dump_component(),
            stop_message_termination.dump_component(),
            token_usage_termination.dump_component(),
            handoff_termination.dump_component(),
            timeout_termination.dump_component(),
            external_termination.dump_component(),
            source_match_termination.dump_component(),
            text_message_termination.dump_component(),
            function_call_termination.dump_component(),
        ]

    def get_assistant_agent_config(self) -> Dict[str, Any]:
        """
        Get an assistant config by ID.

        Args:
            assistant_id: ID of the assistant to get
        """
        model_client = AzureOpenAIChatCompletionClient(
            azure_deployment="{your-azure-deployment}",
            model="gpt-4o-mini",
            api_version="2024-06-01",
            azure_endpoint="https://{your-custom-endpoint}.openai.azure.com/",
            api_key="test",
        )

        async def function_tool_func(x: str):
            return x + "_world"

        assistant_agent = AssistantAgent(
            name="assistant_agent",
            system_message="You are a helpful assistant.",
            description="an agent that helps the user",
            model_client=model_client,
            workbench=None,
        )
        assistant_agent._workbench = None
        assistant_agent._tools = [
            FunctionTool(
                name="function_tool",
                description="a function tool",
                func=function_tool_func,
            )
        ]
        user_proxy_agent = UserProxyAgent(
            name="user_proxy",
            description="a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
        )
        return [assistant_agent.dump_component(), user_proxy_agent.dump_component()]

    def get_user_proxy_config(self) -> Dict[str, Any]:
        """
        Get a user proxy config by ID.

        Args:
            user_proxy_id: ID of the user proxy to get
        """
        user_proxy_agent = UserProxyAgent(
            name="user_proxy",
            description="a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
        )
        return user_proxy_agent.dump_component()

    def get_team_configs(self) -> List[Dict[str, Any]]:
        """
        Get all team configs.
        """
        model_client = OpenAIChatCompletionClient(
            model="gpt-4", api_key="{your-openai-key}"
        )
        assistant_agent = AssistantAgent(
            name="assistant_agent",
            system_message="You are a helpful assistant.",
            description="an agent that helps the user",
            model_client=model_client,
            handoffs=["user_proxy"],
        )
        user_proxy_agent = UserProxyAgent(
            name="user_proxy",
            description="a human user that should be consulted only when the assistant_agent is unable to verify the information provided by the websurfer_agent",
        )
        round_robin_group_chat = RoundRobinGroupChat(
            termination_condition=[],
            participants=[assistant_agent, user_proxy_agent],
            max_turns=10,
            custom_message_types=[],
            emit_team_events=False,
        )
        selector_group_chat = SelectorGroupChat(
            participants=[assistant_agent, user_proxy_agent],
            model_client=model_client,
            termination_condition=None,
            max_turns=10,
            runtime=None,
            selector_prompt="""You are in a role play game. The""",
            allow_repeated_speaker=False,
            max_selector_attempts=3,
            selector_func=None,
            candidate_func=None,
            custom_message_types=None,
            emit_team_events=False,
            model_client_streaming=False,
            model_context=None,
        )
        swarm = Swarm(
            participants=[assistant_agent, user_proxy_agent],
            termination_condition=None,
            max_turns=10,
            runtime=None,
            custom_message_types=None,
            emit_team_events=False,
        )
        return [
            round_robin_group_chat.dump_component(),
            selector_group_chat.dump_component(),
            swarm.dump_component(),
        ]

    def get_tool_configs(self) -> List[Dict[str, Any]]:
        """
        Get all tool configs.
        """
        python_code_execution_tool = PythonCodeExecutionTool(
            LocalCommandLineCodeExecutor(work_dir="coding")
        )

        def tool_func(x: str):
            return x + "_world"

        function_tool = FunctionTool(
            name="function_tool", description="a function tool", func=tool_func
        )

        return [
            python_code_execution_tool.dump_component(),
            function_tool.dump_component(),
        ]

    def get_input_configs(self) -> List[Dict[str, Any]]:
        """
        Get all input configs.
        """
        file_input_model = ComponentModel(
            provider="autogen_core.io.FileInput",
            component_type="input",
            version=1,
            component_version=1,
            description="File input component for reading various file types",
            label="FileInput",
            config=FileInputConfig(
                file_path="data/input.txt", encoding="utf-8", file_type="txt"
            ).model_dump(),
        )

        url_input_model = ComponentModel(
            provider="autogen_core.io.URLInput",
            component_type="input",
            version=1,
            component_version=1,
            description="URL input component for fetching data from APIs",
            label="URLInput",
            config=URLInputConfig(
                url="https://api.example.com/data",
                headers={"Authorization": "Bearer token"},
                timeout=30,
                verify_ssl=True,
            ).model_dump(),
        )

        text_input_model = ComponentModel(
            provider="autogen_core.io.TextInput",
            component_type="input",
            version=1,
            component_version=1,
            description="Text input component for direct text input",
            label="TextInput",
            config=TextInputConfig(
                content="Hello, world!", encoding="utf-8"
            ).model_dump(),
        )

        chat_input_model = ComponentModel(
            provider="autogen_core.io.ChatInput",
            component_type="input",
            version=1,
            component_version=1,
            description="Text input component for direct text input",
            label="ChatInput",
            config=ChatInputConfig(text="Hello, world!").model_dump(),
        )

        image_input_model = ComponentModel(
            provider="autogen_core.io.ImageInput",
            component_type="input",
            version=1,
            component_version=1,
            description="Image input component for handling image data",
            label="ImageInput",
            config=ImageInputConfig(
                source="image.png", source_type="file", format="png"
            ).model_dump(),
        )
        return [
            file_input_model.model_dump(),
            url_input_model.model_dump(),
            text_input_model.model_dump(),
            chat_input_model.model_dump(),
            image_input_model.model_dump(),
        ]

    def get_output_configs(self) -> List[Dict[str, Any]]:
        """
        Get all output configs.
        """
        json_output_model = ComponentModel(
            provider="autogen_core.io.JSONOutput",
            component_type="output",
            version=1,
            component_version=1,
            description="JSON output component for writing JSON data",
            label="JSONOutput",
            config=JSONOutputConfig(
                file_path="data/output.json", encoding="utf-8"
            ).model_dump(),
        )

        text_output_model = ComponentModel(
            provider="autogen_core.io.TextOutput",
            component_type="output",
            version=1,
            component_version=1,
            description="Text output component for writing text data",
            label="TextOutput",
            config=TextOutputConfig(
                content="Hello, world!", encoding="utf-8"
            ).model_dump(),
        )

        markdown_output_model = ComponentModel(
            provider="autogen_core.io.MarkdownOutput",
            component_type="output",
            version=1,
            component_version=1,
            description="Markdown output component for writing Markdown data",
            label="MarkdownOutput",
            config=MarkdownOutputConfig(
                content="# Hello, world!", encoding="utf-8"
            ).model_dump(),
        )

        return [
            json_output_model.model_dump(),
            text_output_model.model_dump(),
            markdown_output_model.model_dump(),
        ]
