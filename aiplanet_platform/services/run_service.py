"""
Service for run operations
"""
import json
import base64
import io
import logging
from datetime import datetime, timedelta
from uuid import UUID
from typing import Any, Dict, List, Optional
from sqlalchemy.orm import Session, joinedload
from fastapi import Depends, HTTPException, status
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.base import TaskResult
from azure.core.credentials import AzureKeyCredential
from azure.ai.formrecognizer import DocumentAnalysisClient
from temporalio.common import WorkflowIDReusePolicy

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.run import Run
from aiplanet_platform.schemas.run import RunChatMessage
from aiplanet_platform.services.team_service import TeamService
from aiplanet_platform.services.session_service import SessionService
from aiplanet_platform.services.message_service import MessageService
from aiplanet_platform.services.user_service import UserService
from aiplanet_platform.services.team_manager_service import TeamManagerService
from aiplanet_platform.constants.team_manager import (
    <PERSON><PERSON><PERSON><PERSON>,
    SettingsConfig,
)
from aiplanet_platform.constants.run_context import RunStatus
from aiplanet_platform.jobs.temporal_streaming_workflow import (
    TeamStreamingWorkflow,
    TeamStreamingWorkflowInput,
    TeamStreamingWorkflowResult,
)
from aiplanet_platform.core.temporal_config import TemporalClient
from aiplanet_platform.core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class RunService:
    """Service for run operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = Run
        self.team_service = TeamService(db)
        self.session_service = SessionService(db)
        self.message_service = MessageService(db)
        self.user_service = UserService(db)
        self.team_manager_service = TeamManagerService(db)
        self.doc_intel_client = DocumentAnalysisClient(
            endpoint=settings.DOCUMENT_INTEL_ENDPOINT,
            credential=AzureKeyCredential(settings.DOCUMENT_INTEL_KEY),
        )

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[Run]:
        """
        Fetch a run by ID.

        Args:
            resource_id: ID of the run

        Returns:
            Run object if found, None otherwise
        """
        try:
            return (
                self.db.query(Run)
                .filter(Run.id == resource_id, Run.is_deleted.is_(False))
                .options(joinedload(Run.session), joinedload(Run.user))
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[Run]:
        """
        Fetch runs by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)

        Returns:
            List of Run objects
        """
        try:
            query = self.db.query(Run).filter(Run.is_deleted.is_(False))

            # Apply filters
            for key, value in filters.items():
                if hasattr(Run, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]  # Remove '_like' suffix
                        query = query.filter(
                            getattr(Run, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]  # Remove '_gt' suffix
                        query = query.filter(getattr(Run, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]  # Remove '_lt' suffix
                        query = query.filter(getattr(Run, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]  # Remove '_gte' suffix
                        query = query.filter(getattr(Run, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]  # Remove '_lte' suffix
                        query = query.filter(getattr(Run, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]  # Remove '_in' suffix
                        query = query.filter(getattr(Run, field_name).in_(value))
                    else:
                        query = query.filter(getattr(Run, key) == value)

            # Apply sorting
            if sort_by and hasattr(Run, sort_by):
                order_field = getattr(Run, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            return query.all()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> Run:
        """
        Create a new run.

        Args:
            data: Dictionary of run data

        Returns:
            Created Run object
        """
        try:
            resource = Run(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[Run]:
        """
        Update a run by ID.

        Args:
            resource_id: ID of the run
            data: Dictionary of data to update

        Returns:
            Updated Run object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[Run]:
        """
        Update runs by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated Run objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a run by ID.

        Args:
            resource_id: ID of the run

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete runs by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a run by ID.

        Args:
            resource_id: ID of the run

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(Run).filter(Run.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    async def chat_run(self, run_id: UUID, user_id: UUID, chat: RunChatMessage) -> str:
        """
        Chat with a run using Temporal workflow integration.

        Args:
            run_id: ID of the run
            user_id: ID of the user
            chat: Chat message

        Returns:
            Chat response
        """
        try:
            run = self.fetch_resource_by_id(run_id)
            if not run:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Run not found",
                )

            session = self.session_service.fetch_resource_by_id(run.session_id)
            team = session.team
            if not team or team.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Team not found",
                )

            # Get team configuration
            team_config_path = self.team_service.generate_workflow(team)
            team_config = json.load(open(team_config_path, "r"))
            if not team_config:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Team config not found",
                )

            # Save user message
            self.message_service.create_resource(
                {
                    "run_id": run_id,
                    "user_id": user_id,
                    "session_id": run.session_id,
                    "config": {
                        "content": [
                            task.model_dump() if hasattr(task, "model_dump") else task
                            for task in chat.inputs
                        ],
                        "source": "user",
                        "message_type": "user_message",
                    },
                }
            )

            # Execute with Temporal or legacy mode
            if settings.TEMPORAL_ENABLED:
                system_output = await self._chat_run_with_temporal(
                    run_id, user_id, chat, team_config, team
                )
            else:
                system_output = await self._chat_run_legacy(
                    run_id, user_id, chat, team_config, team
                )

            return system_output

        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Chat run error: {str(e)}",
            ) from e

    async def _chat_run_with_temporal(
        self,
        run_id: UUID,
        user_id: UUID,
        chat: RunChatMessage,
        team_config: Dict[str, Any],
        team: Any,
    ) -> str:
        """Execute chat run using Temporal workflow"""
        try:
            logger.info(f"Starting Temporal workflow for chat run {run_id}")

            # Get run details and environment variables
            run = self.fetch_resource_by_id(run_id)
            env_vars = None

            if run and run.user_id:
                user = self.user_service.fetch_resource_by_id(run.user_id)
                env_vars = (
                    SettingsConfig(**user.settings.config).environment
                    if user.settings and user.settings.config
                    else None
                )

                # Update run task and status
                # constructed_task = self.construct_task(chat)
                # run.task = self._convert_images_in_dict(
                #     MessageConfig(content=constructed_task, source="user").model_dump()
                # )
                run.status = RunStatus.ACTIVE
                self.update_resource_by_id(run_id, run.__dict__)

            # Prepare workflow input
            clean_task = [
                task.model_dump() if hasattr(task, "model_dump") else task
                for task in self.construct_task(chat)
            ]
            workflow_input = TeamStreamingWorkflowInput(
                run_id=str(run_id),
                task=clean_task,
                team_config=team_config,
                env_vars=[
                    var.model_dump() if hasattr(var, "model_dump") else var
                    for var in env_vars
                ]
                if env_vars
                else None,
                user_id=str(user_id),
                team_state=run.team_result if run.team_result else None,
            )

            # Get Temporal client
            client = await TemporalClient.get_client()
            config = TemporalClient.get_config()

            # Start workflow
            workflow_id = f"team-chat-{run_id}-{datetime.now().isoformat()}"
            workflow_handle = await client.start_workflow(
                TeamStreamingWorkflow.run,
                workflow_input,
                id=workflow_id,
                task_queue=config.task_queue,
                execution_timeout=timedelta(hours=1),
                id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE_FAILED_ONLY,
            )

            logger.info(
                f"Started Temporal workflow {workflow_id} for chat run {run_id}"
            )

            # Wait for workflow completion
            result: TeamStreamingWorkflowResult = await workflow_handle.result()

            logger.info(f"Workflow completed for chat run {run_id}")

            # Process the result
            if result.status == "completed" and result.final_result:
                # Update run with final result
                # run.team_result = self._convert_images_in_dict(result.final_result)
                run.status = RunStatus.COMPLETE
                run.team_result = result.team_state
                self.update_resource_by_id(run_id, run.__dict__)

                # Extract system output
                system_output = self._extract_system_output(result.final_result)

                # Save system message
                self.message_service.create_resource(
                    {
                        "run_id": run_id,
                        "user_id": user_id,
                        "session_id": run.session_id,
                        "config": {"content": system_output, "source": "system"},
                    }
                )

                return self.format_team_outputs(team.team_outputs, system_output)

            elif result.status == "failed":
                # Handle failure
                run.status = RunStatus.ERROR
                run.error_message = result.error
                self.update_resource_by_id(run_id, run.__dict__)

                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Workflow failed: {result.error}",
                )

            else:
                # Handle unexpected status
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Unexpected workflow status: {result.status}",
                )

        except Exception as e:
            logger.error(
                f"Failed to execute Temporal workflow for chat run {run_id}: {e}"
            )
            # Fallback to legacy mode
            logger.info(f"Falling back to legacy mode for chat run {run_id}")
            return await self._chat_run_legacy(run_id, user_id, chat, team_config, team)

    async def _chat_run_legacy(
        self,
        run_id: UUID,
        user_id: UUID,
        chat: RunChatMessage,
        team_config: Dict[str, Any],
        team: Any,
    ) -> str:
        """Execute chat run using legacy TeamManagerService"""
        try:
            logger.info(f"Starting legacy execution for chat run {run_id}")

            run = self.fetch_resource_by_id(run_id)
            team_manager = TeamManagerService(db=self.db)
            response = None

            # Get current team state from run
            current_state = run.team_result if run.team_result else None

            async for message in team_manager.run_stream(
                task=self.construct_task(chat),
                team_config=team_config,
                input_func=None,
                cancellation_token=None,
                env_vars=None,
                state=current_state,
            ):
                if isinstance(message, (TaskResult, TeamResult)):
                    response = message

            # Save updated state
            run.team_result = await team_manager.save_state()
            run.status = RunStatus.COMPLETE
            self.update_resource_by_id(run_id, run.__dict__)

            # Extract system output
            system_output = ""
            if isinstance(response, TaskResult):
                system_output = (
                    response.messages[-1].content if response.messages else ""
                )
            elif isinstance(response, TeamResult):
                task_result = response.task_result
                if task_result and task_result.messages:
                    system_output = task_result.messages[-1].content

            # Save system message
            self.message_service.create_resource(
                {
                    "run_id": run_id,
                    "user_id": user_id,
                    "session_id": run.session_id,
                    "config": {"content": system_output, "source": "system"},
                }
            )

            return self.format_team_outputs(team.team_outputs, system_output)

        except Exception as e:
            logger.error(f"Legacy execution failed for chat run {run_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Legacy execution failed: {str(e)}",
            ) from e

    def _extract_system_output(self, final_result: Dict[str, Any]) -> str:
        """Extract system output from final result"""
        try:
            if "task_result" in final_result:
                task_result = final_result["task_result"]
                if "messages" in task_result and task_result["messages"]:
                    return task_result["messages"][-1].get("content", "")

            # Fallback extraction methods
            if "messages" in final_result and final_result["messages"]:
                return final_result["messages"][-1].get("content", "")

            return "No output generated"
        except Exception as e:
            logger.error(f"Error extracting system output: {e}")
            return "Error extracting output"

    def _convert_images_in_dict(self, obj: Any) -> Any:
        """Recursively find and convert Image and datetime objects in dictionaries and lists"""
        if isinstance(obj, dict):
            return {k: self._convert_images_in_dict(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_images_in_dict(item) for item in obj]
        elif isinstance(obj, (datetime,)):
            return obj.isoformat()
        else:
            return obj

    # TODO: Remove only when new implementation works as expected
    # async def chat_run(self, run_id: UUID, user_id: UUID, chat: RunChatMessage) -> str:
    #     """
    #     Chat with a run.

    #     Args:
    #         run_id: ID of the run
    #         chat: Chat message

    #     Returns:
    #         Chat response
    #     """
    #     try:
    #         run = self.fetch_resource_by_id(run_id)
    #         session = self.session_service.fetch_resource_by_id(run.session_id)
    #         if not run:
    #             raise HTTPException(
    #                 status_code=status.HTTP_404_NOT_FOUND,
    #                 detail="Run not found",
    #             )

    #         team = session.team
    #         if not team or team.is_deleted:
    #             raise HTTPException(
    #                 status_code=status.HTTP_404_NOT_FOUND,
    #                 detail="Team not found",
    #             )

    #         team_config_path = self.team_service.generate_workflow(team)
    #         team_config = json.load(
    #             open(
    #                 team_config_path,
    #                 "r",
    #             )
    #         )
    #         if not team_config:
    #             raise HTTPException(
    #                 status_code=status.HTTP_404_NOT_FOUND,
    #                 detail="Team config not found",
    #             )
    #         # Save user message
    #         self.message_service.create_resource(
    #             {
    #                 "run_id": run_id,
    #                 "user_id": user_id,
    #                 "config": chat.model_dump(),
    #             }
    #         )

    #         team_manager = TeamManagerService(db=self.db)
    #         response = None

    #         async for message in team_manager.run_stream(
    #             task=self.construct_task(chat),
    #             team_config=team_config,
    #             input_func=None,
    #             cancellation_token=None,
    #             env_vars=None,
    #             state=run.team_result,
    #         ):
    #             if isinstance(message, TaskResult):
    #                 response = message

    #         run.team_result = await team_manager.save_state()
    #         self.db.add(run)
    #         self.db.commit()
    #         self.db.refresh(run)
    #         system_output = ""
    #         if isinstance(response, TaskResult):
    #             system_output = response.messages[-1].content
    #         self.message_service.create_resource(
    #             {
    #                 "run_id": run_id,
    #                 "user_id": user_id,
    #                 "config": {"content": system_output, "source": "system"},
    #             }
    #         )

    #         return self.format_team_outputs(team.team_outputs, system_output)
    #     except Exception as e:
    #         self.db.rollback()
    #         raise HTTPException(
    #             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    #             detail=f"Database error: {str(e)}",
    #         ) from e

    def format_team_outputs(
        self, team_outputs: Dict[str, Any], system_output: str
    ) -> str:
        """
        Format team outputs.

        Args:
            team_outputs: Dictionary of team outputs
            system_output: System output

        Returns:
            Formatted team outputs
        """
        output = team_outputs[0]
        generated_output = {"source": "system"}
        if output.component["provider"] == "autogen_core.io.JSONOutput":
            generated_output[
                "content"
            ] = f"```json\n{json.dumps(system_output, indent=2)}\n```"
        elif output.component["provider"] == "autogen_core.io.JSONOutput":
            generated_output["content"] = {"result": system_output}
        elif output.component["provider"] == "autogen_core.io.TextOutput":
            generated_output["content"] = system_output
        elif output.component["provider"] == "autogen_core.io.Markdown":
            generated_output["content"] = f"```markdown\n{system_output}\n```"
        else:
            generated_output["content"] = system_output
        return generated_output["content"]

    def construct_task(self, chat_data: RunChatMessage) -> Dict[str, Any]:
        """
        Construct a task from inputs.

        Args:
            inputs: Dictionary of inputs

        Returns:
            Task dictionary
        """
        task = []
        for input in chat_data.inputs:
            if input.type == "TextInput":
                task.append(TextMessage(source="user", content=input.content))
            elif input.type == "FileInput":
                input.content = base64.b64decode(input.content)
                if input.file_type == "application/pdf":
                    # 1) Decode the PDF bytes
                    stream = io.BytesIO(input.content)

                    # 2) Invoke Azure’s “prebuilt-document” model to get structured text
                    poller = self.doc_intel_client.begin_analyze_document(
                        "prebuilt-document", document=stream
                    )
                    result = poller.result()

                    # 3) Concatenate everything into one big string (you could also
                    #    chunk it by page or section, depending on your needs)
                    full_text = []
                    for page in result.pages:
                        for line in page.lines:
                            full_text.append(line.content)
                    document_text = "\n".join(full_text)

                    task.append(
                        TextMessage(
                            source="user",
                            content=document_text,
                            metadata={"file_type": input.file_type},
                        )
                    )
                elif input.file_type == "application/json":
                    task.append(
                        TextMessage(
                            source="user",
                            content=input.content,
                            metadata={"file_type": input.file_type},
                        )
                    )
                elif input.file_type == "text/plain":
                    task.append(
                        TextMessage(
                            source="user",
                            content=input.content,
                            metadata={"file_type": input.file_type},
                        )
                    )
                elif input.file_type == "text/x-python":
                    task.append(
                        TextMessage(
                            source="user",
                            content=input.content,
                            metadata={"file_type": input.file_type},
                        )
                    )
                elif input.file_type == "text/csv":
                    task.append(
                        TextMessage(
                            source="user",
                            content=input.content,
                            metadata={"file_type": input.file_type},
                        )
                    )
                elif input.file_type == "text/x-javascript":
                    task.append(
                        TextMessage(
                            source="user",
                            content=input.content,
                            metadata={"file_type": input.file_type},
                        )
                    )

            elif input.type == "URLInput":
                task.append(TextMessage(source="user", content=input.content))
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid input type",
                )
        return task
