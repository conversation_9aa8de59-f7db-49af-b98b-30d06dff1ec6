"""
Service for user operations
"""
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload

from aiplanet_platform.core.database import get_db
from aiplanet_platform.models.user import User
from aiplanet_platform.services.tool_service import ToolService


class UserService:
    """Service for user operations"""

    def __init__(self, db: Session = Depends(get_db)):
        """
        Initialize the service with a database session.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.model = User

    def fetch_resource_by_id(self, resource_id: UUID) -> Optional[User]:
        """
        Fetch a user by ID.

        Args:
            resource_id: ID of the user

        Returns:
            User object if found, None otherwise
        """
        try:
            return (
                self.db.query(User)
                .filter(User.id == resource_id, User.is_deleted.is_(False))
                .options(joinedload(User.settings))
                .first()
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def fetch_resource_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        sort_by: Optional[str] = None,
        sort_order: str = "asc",
    ) -> List[User]:
        """
        Fetch users by filters.

        Args:
            filters: Dictionary of filters to apply
            skip: Number of records to skip
            limit: Maximum number of records to return
            sort_by: Field to sort by
            sort_order: Sort order (asc or desc)

        Returns:
            List of User objects
        """
        try:
            query = self.db.query(User).filter(User.is_deleted.is_(False))

            # Apply filters
            for key, value in filters.items():
                if hasattr(User, key):
                    if key.endswith("_like"):
                        field_name = key[:-5]  # Remove '_like' suffix
                        query = query.filter(
                            getattr(User, field_name).ilike(f"%{value}%")
                        )
                    elif key.endswith("_gt"):
                        field_name = key[:-3]  # Remove '_gt' suffix
                        query = query.filter(getattr(User, field_name) > value)
                    elif key.endswith("_lt"):
                        field_name = key[:-3]  # Remove '_lt' suffix
                        query = query.filter(getattr(User, field_name) < value)
                    elif key.endswith("_gte"):
                        field_name = key[:-4]  # Remove '_gte' suffix
                        query = query.filter(getattr(User, field_name) >= value)
                    elif key.endswith("_lte"):
                        field_name = key[:-4]  # Remove '_lte' suffix
                        query = query.filter(getattr(User, field_name) <= value)
                    elif key.endswith("_in"):
                        field_name = key[:-3]  # Remove '_in' suffix
                        query = query.filter(getattr(User, field_name).in_(value))
                    else:
                        query = query.filter(getattr(User, key) == value)

            # Apply sorting
            if sort_by and hasattr(User, sort_by):
                order_field = getattr(User, sort_by)
                if sort_order.lower() == "desc":
                    order_field = order_field.desc()
                query = query.order_by(order_field)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            return query.all()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def create_resource(self, data: Dict[str, Any]) -> User:
        """
        Create a new user.

        Args:
            data: Dictionary of user data

        Returns:
            Created User object
        """
        try:
            resource = User(**data)
            self.db.add(resource)
            self.db.commit()
            self.db.refresh(resource)
            tool_service = ToolService(self.db)
            tool_service.bulk_add_tools(resource.id, resource.organization_id)

            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_id(
        self, resource_id: UUID, data: Dict[str, Any]
    ) -> Optional[User]:
        """
        Update a user by ID.

        Args:
            resource_id: ID of the user
            data: Dictionary of data to update

        Returns:
            Updated User object if found, None otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return None

            for key, value in data.items():
                if hasattr(resource, key):
                    setattr(resource, key, value)

            self.db.commit()
            self.db.refresh(resource)
            return resource
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def update_resource_by_filters(
        self, filters: Dict[str, Any], data: Dict[str, Any]
    ) -> List[User]:
        """
        Update users by filters.

        Args:
            filters: Dictionary of filters to apply
            data: Dictionary of data to update

        Returns:
            List of updated User objects
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                for key, value in data.items():
                    if hasattr(resource, key):
                        setattr(resource, key, value)

            self.db.commit()

            # Refresh all resources
            for resource in resources:
                self.db.refresh(resource)

            return resources
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Soft delete a user by ID.

        Args:
            resource_id: ID of the user

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.fetch_resource_by_id(resource_id)
            if not resource:
                return False

            resource.is_deleted = True
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def remove_resources_by_filters(self, filters: Dict[str, Any]) -> int:
        """
        Soft delete users by filters.

        Args:
            filters: Dictionary of filters to apply

        Returns:
            Number of resources deleted
        """
        try:
            resources = self.fetch_resource_by_filters(filters)

            for resource in resources:
                resource.is_deleted = True

            self.db.commit()
            return len(resources)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def hard_delete_resource_by_id(self, resource_id: UUID) -> bool:
        """
        Hard delete a user by ID.

        Args:
            resource_id: ID of the user

        Returns:
            True if deleted, False otherwise
        """
        try:
            resource = self.db.query(User).filter(User.id == resource_id).first()

            if not resource:
                return False

            self.db.delete(resource)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}",
            ) from e

    def count_resources(self, filters: Dict[str, Any] = None) -> int:
        """Count users matching the given filters."""
        try:
            query = self.db.query(self.model)

            if filters:
                for key, value in filters.items():
                    if hasattr(self.model, key):
                        if key.endswith("_like"):
                            field_name = key[:-5]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field.ilike(f"%{value}%"))
                        elif key.endswith("_gt"):
                            field_name = key[:-3]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field > value)
                        elif key.endswith("_lt"):
                            field_name = key[:-3]
                            if hasattr(self.model, field_name):
                                field = getattr(self.model, field_name)
                                query = query.filter(field < value)
                        else:
                            field = getattr(self.model, key)
                            query = query.filter(field == value)

            # Add soft delete filter if applicable
            if hasattr(self.model, "is_deleted"):
                query = query.filter(self.model.is_deleted.is_(False))

            return query.count()

        except Exception as e:
            raise Exception(f"Failed to count users: {str(e)}")

    def validate_and_update_user_by_id(self, user_id: UUID) -> User:
        try:
            user = (
                self.db.query(User)
                .filter(User.id == user_id, User.is_deleted.is_(False))
                .first()
            )
            if not user:
                return False
            user.update_usage()
            self.db.commit()
            self.db.refresh(user)
            return user
        except Exception as e:
            raise Exception(f"Failed to validate user: {str(e)}")
