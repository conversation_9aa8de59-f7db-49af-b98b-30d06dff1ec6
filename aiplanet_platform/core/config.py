"""
Application configuration
"""
import secrets
from functools import lru_cache
from typing import List, Optional, Union

from pydantic import AnyHttpUrl, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""

    # API settings
    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "aiplanet_platform"
    DEBUG: bool = False

    # CORS settings
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    ALLOWED_HOSTS: List[str] = ["*"]

    # Security settings
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_hex(32))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    ALGORITHM: str = "HS256"

    # Database settings
    DATABASE_URL: str = (
        "postgresql://postgres:postgres@localhost:5432/aiplanet_platform"
    )
    SQL_ECHO: bool = False
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 1800

    # Cache settings
    REDIS_URL: Optional[str] = None
    CACHE_TTL: int = 300  # 5 minutes

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 100

    # Azure Document Intelligence Config
    DOCUMENT_INTEL_ENDPOINT: str = ""
    DOCUMENT_INTEL_KEY: str = ""

    # OAuth Settings
    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GITHUB_CLIENT_ID: str = ""
    GITHUB_CLIENT_SECRET: str = ""

    # OAuth redirect URIs
    FRONTEND_URL: str = "http://localhost:3000"  # Your frontend URL
    OAUTH_REDIRECT_URI: str = "/auth/callback"

    # Temporal Worker
    TEMPORAL_START_WORKER: bool = True

    # Temporal Configuration
    TEMPORAL_ENABLED: bool = True
    TEMPORAL_HOST: str = "localhost:7233"
    TEMPORAL_NAMESPACE: str = "default"
    TEMPORAL_TASK_QUEUE: str = "team-manager-tasks"

    # Optional TLS configuration for production
    TEMPORAL_TLS_ENABLED: bool = False
    TEMPORAL_TLS_CERT_PATH: str = ""
    TEMPORAL_TLS_KEY_PATH: str = ""
    TEMPORAL_TLS_CA_PATH: str = ""

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Pydantic v2 uses model_config instead of Config class
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore"
    )


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings instance
    """
    return Settings()
