"""
Organization model
"""
import uuid
import re
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, DateTime, Enum, String, Text
from sqlalchemy.orm import relationship, validates
from sqlalchemy.sql import func

from aiplanet_platform.constants.organization import OrganizationStatus
from aiplanet_platform.core.database import Base
from aiplanet_platform.models.agent import Agent
from aiplanet_platform.models.input import Input
from aiplanet_platform.models.model import Model
from aiplanet_platform.models.output import Output
from aiplanet_platform.models.team import Team
from aiplanet_platform.models.tool import Tool
from aiplanet_platform.models.user import User
from aiplanet_platform.models.termination_condition import TerminationCondition
from aiplanet_platform.models.api_key import APIKey

# Email regex
email_regex = re.compile(r"^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$")


class Organization(Base):
    """Organization database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "Organizations".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Organization email
    email = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        default="<EMAIL>",
    )

    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Organization Status
    status = Column(
        Enum(OrganizationStatus),
        default=OrganizationStatus.PENDING,
        nullable=False,
        index=True,
    )

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # 1 organization → many users
    users = relationship(
        User,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 2 organization → many agents
    agents = relationship(
        Agent,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 3 organization → many models
    models = relationship(
        Model,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 4 organization → many tools
    tools = relationship(
        Tool,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 5 organization → many teams
    teams = relationship(
        Team,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 6 organization → many inputs
    inputs = relationship(
        Input,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    # 7 organization → many outputs
    outputs = relationship(
        Output,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    terminationconditions = relationship(
        TerminationCondition,
        back_populates="organization",
        cascade="all, delete-orphan",
    )

    api_keys = relationship(
        APIKey, back_populates="organization", cascade="all, delete-orphan"
    )

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("organizations.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("Organization", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("Organization", back_populates="children", remote_side=[id])

    def __repr__(self):
        return "<Organization(id={}, name={})>".format(self.id, self.name)

    @validates("email")
    def validate_email(self, key, address):
        if not email_regex.match(address):
            raise ValueError(f"Invalid email address: {address!r}")
        return address
