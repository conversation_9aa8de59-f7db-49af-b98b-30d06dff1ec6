"""
Job Execution model
"""
from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    ForeignKey,
    UUID,
    String,
    Float,
    Text,
    Enum,
)
from sqlalchemy.sql import func
import uuid

from aiplanet_platform.core.database import Base


class JobExecutionStatus(str, Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobExecution(Base):
    """JobExecution database model"""

    # Set table name based on class name with proper pluralization
    __tablename__ = "JobExecutions".lower()

    # Primary key
    id = Column(UUID, primary_key=True, index=True, default=uuid.uuid4)

    # Basic fields
    run_id = Column(UUID, ForeignKey("runs.id"), nullable=True)
    status = Column(String(50), default="queued", nullable=True)

    # AWS-specific fields
    lambda_request_id = Column(String(255), nullable=True)
    s3_input_key = Column(String(500), nullable=True)
    s3_output_key = Column(String(500), nullable=True)
    sqs_message_id = Column(String(255), nullable=True)

    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    current_step = Column(String(255), nullable=True)

    # Results
    result_data = Column(Text, nullable=True)  # For small results
    error_message = Column(Text, nullable=True)

    queued_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    # Timestamps
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Soft delete flag - Always include this in models
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)

    # Add your custom fields here
    # Examples:
    # status = Column(String(50), default="active", nullable=False)
    # price = Column(Float, nullable=True)
    # is_active = Column(Boolean, default=True, nullable=False)
    # parent_id = Column(UUID, ForeignKey("job_executions.id"), nullable=True)

    # Relationships
    # Example:
    # children = relationship("JobExecution", back_populates="parent", cascade="all, delete-orphan")
    # parent = relationship("JobExecution", back_populates="children", remote_side=[id])

    def __repr__(self):
        try:
            id_val = (
                self.__dict__.get("id", "<unknown>")
                if hasattr(self, "__dict__")
                else "<unknown>"
            )
            return f"<Agent(id={id_val})>"
        except Exception:
            return "<Agent(detached)>"
