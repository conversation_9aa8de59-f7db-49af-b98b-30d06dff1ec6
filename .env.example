# aiplanet_platform - Environment Variables
# Copy this file to .env and modify as needed

# API settings
PROJECT_NAME="aiplanet_platform"
DEBUG=true
API_V1_PREFIX=/api/v1

# Security
SECRET_KEY="fd44c0aa45613a3095fa7f0a975071feae62485dae1c1e935ff3173a3bf35407"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000","http://localhost"]

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/aiplanet_platform
SQL_ECHO=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Redis (optional)
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=300

# Logging
LOG_LEVEL=INFO

# Rate limiting
RATE_LIMIT_PER_MINUTE=100

# Docker settings
DOCKER_TARGET=development  # development or production
APP_PORT=8000
DB_PORT=5432
REDIS_PORT=6379
PGADMIN_PORT=5050
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=aiplanet_platform
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

# Azure Document Intelligence
DOCUMENT_INTEL_ENDPOINT=https://your_endpoint.cognitiveservices.azure.com/
DOCUMENT_INTEL_KEY=your_key

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth  
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Frontend URL (for OAuth redirects)
FRONTEND_URL=http://localhost:3000
OAUTH_REDIRECT_URI=/auth/callback

TEMPORAL_START_WORKER=false  # KEY: This tells main server not to start worker
# Temporal Configuration
TEMPORAL_ENABLED=true
TEMPORAL_HOST=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_TASK_QUEUE=team-manager-tasks

# Worker Configuration (for separate worker processes)
WORKER_ID=worker-1

# Optional TLS configuration for production
TEMPORAL_TLS_ENABLED=false
TEMPORAL_TLS_CERT_PATH=
TEMPORAL_TLS_KEY_PATH=
TEMPORAL_TLS_CA_PATH=

# For Temporal Cloud (production), you would set:
# TEMPORAL_HOST=your-namespace.sdvdw.tmprl.cloud:7233
# TEMPORAL_NAMESPACE=your-namespace.sdvdw
# TEMPORAL_TLS_ENABLED=true
# TEMPORAL_TLS_CERT_PATH=/path/to/client.pem
# TEMPORAL_TLS_KEY_PATH=/path/to/client.key