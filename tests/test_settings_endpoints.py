"""
Test for Settings Management Router
"""
# ruff: noqa: F401
import sys
import os
from datetime import datetime
from uuid import uuid4

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_router_import():
    """Test that Settings router can be imported"""
    print("📦 Testing Settings Router Import...")

    try:
        from aiplanet_platform.routers.v1.private.settings import router

        print("✅ Settings router imported successfully")

        # Check that router has the expected attributes
        assert hasattr(router, "prefix"), "Router should have prefix"
        assert hasattr(router, "tags"), "Router should have tags"
        assert router.prefix == "/settings", "Router should have correct prefix"
        assert "settings" in router.tags, "Router should have correct tags"
        print("✅ Router configuration is correct")

        # Check that router has routes
        routes = router.routes
        assert len(routes) > 0, "Router should have routes"
        print(f"✅ Router has {len(routes)} routes")

        return True

    except ImportError as e:
        print(f"❌ Failed to import Settings router: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing router import: {e}")
        return False


def test_router_endpoints():
    """Test that router has expected endpoints"""
    print("\n🛤️  Testing Router Endpoints...")

    try:
        from aiplanet_platform.routers.v1.private.settings import router

        routes = router.routes
        route_paths = [route.path for route in routes]
        route_methods = {}
        
        for route in routes:
            if hasattr(route, 'methods'):
                route_methods[route.path] = list(route.methods)

        print(f"📍 Found routes: {route_paths}")
        print(f"🔧 Route methods: {route_methods}")

        # Expected CRUD endpoints
        expected_endpoints = [
            "/",  # GET (list), POST (create)
            "/{settings_id}",  # GET (read), PUT (update), DELETE (delete)
            "/user/current",  # GET (current user settings)
        ]

        for endpoint in expected_endpoints:
            assert endpoint in route_paths, f"Missing endpoint: {endpoint}"
            print(f"✅ Found endpoint: {endpoint}")

        # Check for CRUD operations
        assert "/" in route_paths, "Missing list/create endpoint"
        assert "/{settings_id}" in route_paths, "Missing individual settings endpoint"
        assert "/user/current" in route_paths, "Missing current user settings endpoint"

        print("✅ All expected endpoints found")
        return True

    except Exception as e:
        print(f"❌ Error testing router endpoints: {e}")
        return False


def test_schemas_import():
    """Test that Settings schemas can be imported"""
    print("\n📋 Testing Settings Schemas Import...")

    try:
        from aiplanet_platform.schemas.settings import (
            SettingsBase,
            SettingsCreate,
            SettingsUpdate,
            SettingsResponse,
            SettingsList,
        )

        print("✅ All settings schemas imported successfully")

        # Test schema instantiation
        base_data = {"config": {"test": "value"}}
        
        # Test SettingsBase
        base_schema = SettingsBase(**base_data)
        assert base_schema.config == {"test": "value"}
        print("✅ SettingsBase schema works")

        # Test SettingsCreate
        create_data = {**base_data, "user_id": uuid4()}
        create_schema = SettingsCreate(**create_data)
        assert create_schema.config == {"test": "value"}
        print("✅ SettingsCreate schema works")

        # Test SettingsUpdate
        update_schema = SettingsUpdate(**base_data)
        assert update_schema.config == {"test": "value"}
        print("✅ SettingsUpdate schema works")

        return True

    except ImportError as e:
        print(f"❌ Failed to import Settings schemas: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        return False


def test_service_import():
    """Test that Settings service can be imported"""
    print("\n🔧 Testing Settings Service Import...")

    try:
        from aiplanet_platform.services.settings_service import SettingsService

        print("✅ SettingsService imported successfully")

        # Check that service has expected methods
        expected_methods = [
            "fetch_resource_by_id",
            "fetch_resource_by_filters", 
            "create_resource",
            "update_resource_by_id",
            "remove_resource_by_id",
        ]

        for method in expected_methods:
            assert hasattr(SettingsService, method), f"Missing method: {method}"
            print(f"✅ Found method: {method}")

        return True

    except ImportError as e:
        print(f"❌ Failed to import SettingsService: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing service import: {e}")
        return False


def run_all_tests():
    """Run all tests"""
    print("🧪 Running Settings Router Tests\n")
    
    tests = [
        test_router_import,
        test_router_endpoints,
        test_schemas_import,
        test_service_import,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
