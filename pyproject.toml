[tool.poetry]
name = "aiplanet_platform"
version = "0.1.0"
description = "A FastAPI project"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.115.12"
uvicorn = {extras = ["standard"], version = "^0.34.3"}
pydantic = "^2.1.0"
sqlalchemy = "^2.0.0"
python-dotenv = "^1.0.0"
alembic = "^1.15.2"
pydantic-settings = "^2.8.1"
autogen-core = "^0.5.7"
autogen-ext = "^0.5.7"
autogen-agentchat = "^0.5.7"
sqlalchemy-utils = "^0.41.2"
cryptography = "^45.0.3"
psycopg2-binary = "^2.9.10"
python-jose = {extras = ["cryptography"], version = "^3.5.0"}
passlib = "^1.7.4"
python-multipart = "^0.0.20"
gunicorn = "^23.0.0"
tiktoken = "^0.9.0"  # ✅ moved here
anthropic = "^0.52.1"
openai = "^1.82.1"
azure-core = "^1.34.0"
azure-identity = "^1.23.0"
bcrypt = "^4.3.0"
authlib = "^1.6.0"
boto3 = "^1.38.46"
aiohttp = "^3.12.13"
temporalio = "^1.13.0"
googlesearch-python = "^1.3.0"
pycountry = "^24.6.1"
langchain-tavily = "^0.2.5"
tavily-python = "^0.7.9"
yfinance = "^0.2.64"
yt-dlp = "^2025.6.30"
youtube-search = "^2.1.2"
wikipedia = "^1.4.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
httpx = "^0.25.0"
flake8 = {version = "^6.1.0", python = ">=3.10"}
black = "^23.7.0"
mypy = "^1.4.0"
isort = "^5.12.0"
coverage = "^7.2.0"
anthropic = "^0.52.1"
aiofiles = "^24.1.0"
tiktoken = "^0.9.0"
openai = "^1.82.1"
azure-core = "^1.34.0"
azure-identity = "^1.23.0"
pre-commit = "^4.2.0"
ruff = "^0.11.12"
azure-ai-documentintelligence = "^1.0.2"
azure-ai-formrecognizer = "^3.3.3"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
ignore_missing_imports = true
check_untyped_defs = true
python_version = "3.10"